#!/usr/bin/env python3
"""
Demo script for DataValidationAgent
This script demonstrates how to use the DataValidationAgent to validate FundHoldings data.
"""

import pandas as pd
import sys
import os

# Add the core module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

from core.data_validation import DataValidationAgent


def create_sample_data():
    """Create sample data for demonstration."""
    
    # Valid sample data
    valid_data = {
        'UNIQUE_ID': ['FUND001', 'FUND002', 'FUND003', 'FUND004'],
        'PORTFOLIO_ID': ['PORT001', 'PORT002', 'PORT003', 'PORT004'],
        'REGISTERED_HOLDER': ['Holder A', 'Holder B', 'Holder C', 'Holder D'],
        'NAV': [1000.50, 2500.75, 3200.00, 1800.25],
        'OWNERSHIP_PERCENTAGE': [15.5, 22.3, 18.7, 12.1],
        'CAPITAL_CALLED': [50000.00, 75000.00, 60000.00, 45000.00],
        'NO_OF_SHARES': [100, 250, 200, 150],
        'COMMITTED_CAPITAL': [100000.00, 150000.00, 120000.00, 90000.00],
        'PERIOD': ['2023-Q1', '2023-Q2', '2023-Q3', '2023-Q4'],
        'FUND_NAME': ['Growth Fund A', 'Value Fund B', 'Tech Fund C', 'Bond Fund D']
    }
    
    # Invalid sample data (for testing validation failures)
    invalid_data = {
        'UNIQUE_ID': ['FUND001', 'FUND002', 'TOTAL_SUMMARY', None],  # Has TOTAL and null
        'PORTFOLIO_ID': ['PORT001', 'PORT002', 'PORT003', 'PORT004'],
        'REGISTERED_HOLDER': ['Holder A', 'Holder B', 'Holder C', 'Holder D'],
        'NAV': [1000.50, 0, 0, 0],  # Row with all zeros
        'OWNERSHIP_PERCENTAGE': [15.5, 0, 0, 0],
        'CAPITAL_CALLED': [50000.00, 0, 0, 0],
        'NO_OF_SHARES': [100, 0, 0, 0],
        'COMMITTED_CAPITAL': [100000.00, 0, 0, 0],
        'PERIOD': ['2023-Q1', '2023-Q2', '2023-Q3', '2023-Q4'],
        'FUND_NAME': ['Growth Fund A', 'Value Fund B', 'Tech Fund C', 'Bond Fund D']
    }
    
    return pd.DataFrame(valid_data), pd.DataFrame(invalid_data)


def demo_validation():
    """Demonstrate the data validation process."""
    
    print("=" * 60)
    print("CBRE Data Validation Agent Demo")
    print("=" * 60)
    
    # Create mock LLM for demo (to avoid OpenAI dependency)
    class MockLLM:
        def invoke(self, prompt):
            class MockResponse:
                content = "Mock LLM validation response"
            return MockResponse()
    
    # Initialize the validation agent
    agent = DataValidationAgent(llm=MockLLM())
    
    # Create sample data
    valid_df, invalid_df = create_sample_data()
    
    print("\n1. TESTING VALID DATA")
    print("-" * 30)
    print("Sample valid data:")
    print(valid_df.head())
    
    # Run validation on valid data
    result = agent.run(valid_df)
    
    print(f"\nValidation Result: {'✅ PASSED' if result['success'] else '❌ FAILED'}")
    print(f"Message: {result['message']}")
    print("\nValidation Logs:")
    for log in result['logs']:
        print(f"  • {log}")
    
    print(f"\nCleaned data shape: {result['cleaned_data'].shape if result['cleaned_data'] is not None else 'N/A'}")
    
    print("\n" + "=" * 60)
    print("\n2. TESTING INVALID DATA")
    print("-" * 30)
    print("Sample invalid data (contains TOTAL row, null UNIQUE_ID, and zero financial values):")
    print(invalid_df.head())
    
    # Run validation on invalid data
    result = agent.run(invalid_df)
    
    print(f"\nValidation Result: {'✅ PASSED' if result['success'] else '❌ FAILED'}")
    print(f"Message: {result['message']}")
    print("\nValidation Logs:")
    for log in result['logs']:
        print(f"  • {log}")
    
    if result['cleaned_data'] is not None:
        print(f"\nCleaned data shape: {result['cleaned_data'].shape}")
        print("Cleaned data:")
        print(result['cleaned_data'])
    
    print("\n" + "=" * 60)
    print("\n3. TESTING INDIVIDUAL VALIDATION METHODS")
    print("-" * 40)
    
    # Test individual methods
    print("\n3.1 File Structure Validation:")
    structure_result = agent._validate_file_structure(valid_df, agent._load_template(), "FundHoldings")
    print(f"Result: {'✅ PASSED' if structure_result['success'] else '❌ FAILED'}")
    print(f"Message: {structure_result['message']}")
    
    print("\n3.2 Mandatory Columns Validation:")
    mandatory_result = agent._validate_mandatory_columns(valid_df, agent._load_template(), "FundHoldings")
    print(f"Result: {'✅ PASSED' if mandatory_result['success'] else '❌ FAILED'}")
    print(f"Message: {mandatory_result['message']}")
    
    print("\n3.3 Data Cleaning:")
    cleaned_df, clean_result = agent._clean_data(invalid_df.copy())
    print(f"Result: {'✅ PASSED' if clean_result['success'] else '❌ FAILED'}")
    print(f"Message: {clean_result['message']}")
    print(f"Original rows: {len(invalid_df)}, Cleaned rows: {len(cleaned_df)}")
    
    print("\n3.4 Final Validation:")
    final_result = agent._final_validation(cleaned_df)
    print(f"Result: {'✅ PASSED' if final_result['success'] else '❌ FAILED'}")
    print(f"Message: {final_result['message']}")
    
    print("\n3.5 Data Type Validation:")
    dtype_result = agent.validate_data_types(valid_df)
    print(f"Result: {'✅ PASSED' if dtype_result['success'] else '❌ FAILED'}")
    print(f"Message: {dtype_result['message']}")
    
    print("\n" + "=" * 60)
    print("Demo completed! 🎉")
    print("=" * 60)


if __name__ == "__main__":
    demo_validation()
