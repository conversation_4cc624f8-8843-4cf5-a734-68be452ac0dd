Metadata-Version: 2.4
Name: aiosignal
Version: 1.4.0
Summary: aiosignal: a list of registered asynchronous callbacks
Home-page: https://github.com/aio-libs/aiosignal
Maintainer: aiohttp team <<EMAIL>>
Maintainer-email: <EMAIL>
License: Apache 2.0
Project-URL: Chat: Gitter, https://gitter.im/aio-libs/Lobby
Project-URL: CI: GitHub Actions, https://github.com/aio-libs/aiosignal/actions
Project-URL: Coverage: codecov, https://codecov.io/github/aio-libs/aiosignal
Project-URL: Docs: RTD, https://docs.aiosignal.org
Project-URL: GitHub: issues, https://github.com/aio-libs/aiosignal/issues
Project-URL: GitHub: repo, https://github.com/aio-libs/aiosignal
Classifier: License :: OSI Approved :: Apache Software License
Classifier: Intended Audience :: Developers
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3 :: Only
Classifier: Development Status :: 5 - Production/Stable
Classifier: Operating System :: POSIX
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: Microsoft :: Windows
Classifier: Framework :: AsyncIO
Requires-Python: >=3.9
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: frozenlist>=1.1.0
Requires-Dist: typing-extensions>=4.2; python_version < "3.13"
Dynamic: license-file

=========
aiosignal
=========

.. image:: https://github.com/aio-libs/aiosignal/workflows/CI/badge.svg
   :target: https://github.com/aio-libs/aiosignal/actions?query=workflow%3ACI
   :alt: GitHub status for master branch

.. image:: https://codecov.io/gh/aio-libs/aiosignal/branch/master/graph/badge.svg?flag=pytest
   :target: https://codecov.io/gh/aio-libs/aiosignal?flags[0]=pytest
   :alt: codecov.io status for master branch

.. image:: https://badge.fury.io/py/aiosignal.svg
   :target: https://pypi.org/project/aiosignal
   :alt: Latest PyPI package version

.. image:: https://readthedocs.org/projects/aiosignal/badge/?version=latest
   :target: https://aiosignal.readthedocs.io/
   :alt: Latest Read The Docs

.. image:: https://img.shields.io/discourse/topics?server=https%3A%2F%2Faio-libs.discourse.group%2F
   :target: https://aio-libs.discourse.group/
   :alt: Discourse group for io-libs

.. image:: https://badges.gitter.im/Join%20Chat.svg
   :target: https://gitter.im/aio-libs/Lobby
   :alt: Chat on Gitter

Introduction
============

A project to manage callbacks in `asyncio` projects.

``Signal`` is a list of registered asynchronous callbacks.

The signal's life-cycle has two stages: after creation its content
could be filled by using standard list operations: ``sig.append()``
etc.

After you call ``sig.freeze()`` the signal is *frozen*: adding, removing
and dropping callbacks is forbidden.

The only available operation is calling the previously registered
callbacks by using ``await sig.send(data)``.

For concrete usage examples see the `Signals
<https://docs.aiohttp.org/en/stable/web_advanced.html#aiohttp-web-signals>
section of the `Web Server Advanced
<https://docs.aiohttp.org/en/stable/web_advanced.html>` chapter of the `aiohttp
documentation`_.


Installation
------------

::

   $ pip install aiosignal


Documentation
=============

https://aiosignal.readthedocs.io/

License
=======

``aiosignal`` is offered under the Apache 2 license.

Source code
===========

The project is hosted on GitHub_

Please file an issue in the `bug tracker
<https://github.com/aio-libs/aiosignal/issues>`_ if you have found a bug
or have some suggestions to improve the library.

.. _GitHub: https://github.com/aio-libs/aiosignal
.. _aiohttp documentation: https://docs.aiohttp.org/
