-- FILE_EXISTS schema

IF FILE_EXTENSION NOT IN ('.xls', '.xlsx') THEN RAISE ERROR;

-- Create secure schema
CREATE SCHEMA IF NOT EXISTS SECURE_DB.SEC_MASTER;

-- Example staging table (generic name)
CREATE OR REPLACE TABLE SECURE_DB.SEC_MASTER.FILE_UPLOAD_STAGE (
    UNIQUE_ID STRING,
    PORTFOLIO_ID STRING,
    REGISTERED_HOLDER STRING,
    NAV NUMBER,
    OWNERSHIP_PERCENTAGE NUMBER,
    CAPITAL_CALLED NUMBER,
    NO_OF_SHARES NUMBER,
    COMMITTED_CAPITAL NUMBER,
    PERIOD STRING,
    FUND_NAME STRING
);

-- Control table for template structure
CREATE OR REPLACE TABLE SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS (
    FILE_TYPE STRING,
    COLUMN_NAME STRING,
    IS_REQUIRED BOOLEAN,
    DATA_TYPE STRING,
    POSITION INT
);

-- File Structure Validation
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_VALIDATE_FILE_STRUCTURE(FILE_TYPE STRING)
RETURNS STRING
LANGUAGE SQL
AS
$$
DECLARE
    COL_COUNT_TEMPLATE INT;
    COL_COUNT_UPLOAD INT;
BEGIN
    -- Count expected columns
    SELECT COUNT(*) INTO COL_COUNT_TEMPLATE 
    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS 
    WHERE FILE_TYPE = FILE_TYPE;

    -- Count actual columns in uploaded stage table
    SELECT COUNT(*) INTO COL_COUNT_UPLOAD 
    FROM INFORMATION_SCHEMA.COLUMNS
    WHERE TABLE_SCHEMA = 'SEC_MASTER'
      AND TABLE_NAME = 'FILE_UPLOAD_STAGE';

    IF COL_COUNT_TEMPLATE != COL_COUNT_UPLOAD THEN
        RETURN 'File structure mismatch. Expected ' || COL_COUNT_TEMPLATE || ' columns, found ' || COL_COUNT_UPLOAD;
    END IF;

    RETURN 'File structure valid';
END;
$$;

-- Mandatory Columns Validation
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_VALIDATE_MANDATORY_COLUMNS(FILE_TYPE STRING)
RETURNS STRING
LANGUAGE SQL
AS
$$
DECLARE
    MISSING_COUNT INT;
BEGIN
    SELECT COUNT(*) INTO MISSING_COUNT
    FROM SECURE_DB.SEC_MASTER.CTRL_FILE_MAPPING_DETAILS c
    LEFT JOIN INFORMATION_SCHEMA.COLUMNS u
    ON c.COLUMN_NAME = u.COLUMN_NAME 
       AND u.TABLE_SCHEMA = 'SEC_MASTER'
       AND u.TABLE_NAME = 'FILE_UPLOAD_STAGE'
    WHERE c.FILE_TYPE = FILE_TYPE 
      AND c.IS_REQUIRED = TRUE 
      AND u.COLUMN_NAME IS NULL;

    IF MISSING_COUNT > 0 THEN
        RETURN 'Missing required columns.';
    END IF;

    RETURN 'All required columns present.';
END;
$$;

-- Data Cleanup
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_CLEAN_DATA()
RETURNS STRING
LANGUAGE SQL
AS
$$
BEGIN
    -- Delete rows with all financial fields null/zero
    DELETE FROM SECURE_DB.SEC_MASTER.FILE_UPLOAD_STAGE
    WHERE (NVL(NAV,0) = 0)
      AND (NVL(OWNERSHIP_PERCENTAGE,0) = 0)
      AND (NVL(CAPITAL_CALLED,0) = 0)
      AND (NVL(NO_OF_SHARES,0) = 0)
      AND (NVL(COMMITTED_CAPITAL,0) = 0);

    -- Delete rows where unique ID starts with 'TOTAL'
    DELETE FROM SECURE_DB.SEC_MASTER.FILE_UPLOAD_STAGE
    WHERE UPPER(UNIQUE_ID) LIKE 'TOTAL%';

    -- Clean unique ID: remove special chars & parentheses
    UPDATE SECURE_DB.SEC_MASTER.FILE_UPLOAD_STAGE
    SET UNIQUE_ID = REGEXP_REPLACE(UNIQUE_ID, '[^A-Za-z0-9]', '', 'g');

    RETURN 'Data cleaned successfully.';
END;
$$;

-- Final Validation
CREATE OR REPLACE PROCEDURE SECURE_DB.SEC_MASTER.SP_FINAL_VALIDATION()
RETURNS STRING
LANGUAGE SQL
AS
$$
DECLARE
    REC_COUNT INT;
    DUP_COUNT INT;
    NULL_UID_COUNT INT;
BEGIN
    -- Check if file is empty
    SELECT COUNT(*) INTO REC_COUNT 
    FROM SECURE_DB.SEC_MASTER.FILE_UPLOAD_STAGE;
    IF REC_COUNT = 0 THEN
        RETURN 'Validation failed: File is empty.';
    END IF;

    -- Check for null unique IDs
    SELECT COUNT(*) INTO NULL_UID_COUNT 
    FROM SECURE_DB.SEC_MASTER.FILE_UPLOAD_STAGE
    WHERE UNIQUE_ID IS NULL OR TRIM(UNIQUE_ID) = '';

    IF NULL_UID_COUNT > 0 THEN
        RETURN 'Validation failed: Unique Identifier missing.';
    END IF;

    -- Check for duplicate unique IDs
    SELECT COUNT(*) INTO DUP_COUNT
    FROM (
        SELECT UNIQUE_ID, COUNT(*)
        FROM SECURE_DB.SEC_MASTER.FILE_UPLOAD_STAGE
        GROUP BY UNIQUE_ID
        HAVING COUNT(*) > 1
    );

    IF DUP_COUNT > 0 THEN
        RETURN 'Validation failed: Duplicate Unique Identifiers.';
    END IF;

    RETURN 'Final validation passed.';
END;
$$;
