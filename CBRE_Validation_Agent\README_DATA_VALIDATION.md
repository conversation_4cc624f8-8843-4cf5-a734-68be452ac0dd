# CBRE Data Validation Agent

## Overview

This document provides a comprehensive analysis and implementation guide for the CBRE Data Validation system, including file validation and data validation components.

## File Analysis Summary

### 1. Validation Rules Comparison

**validation_rules_updated.json vs validation_rules.txt:**

| Aspect | validation_rules_updated.json | validation_rules.txt |
|--------|-------------------------------|----------------------|
| Format | Structured JSON metadata | Raw SQL DDL/DML statements |
| Target Table | `FundHoldings` | `FILE_UPLOAD_STAGE` |
| Content | 4 stored procedures as JSON objects | Same 4 stored procedures as SQL |
| Usage | Programmatic access and metadata | Direct SQL execution |

**Key Difference:** Both files contain the same validation logic but target different tables and are in different formats.

### 2. file_validation.py Explanation

The `FileValidationAgent` class handles **file-level validation**:

- **Purpose**: Validates file format (.xls/.xlsx) and basic schema structure
- **Process**:
  1. Checks file extension compatibility
  2. Loads Excel file using appropriate pandas engine
  3. Loads column template from JSON configuration
  4. Uses LLM to validate file structure against rules and template
  5. Returns validation results with detailed logs

**Scope**: File format validation and basic structure checking (extension + schema validation only)

## Data Validation Implementation

### 3. data_validation.py

The `DataValidationAgent` class implements **data-level validation** based on stored procedure logic:

#### Core Features:
- **File Structure Validation** (SP_VALIDATE_FILE_STRUCTURE)
- **Mandatory Columns Validation** (SP_VALIDATE_MANDATORY_COLUMNS) 
- **Data Cleaning** (SP_CLEAN_DATA)
- **Final Validation** (SP_FINAL_VALIDATION)
- **Additional Data Type Validation**

#### Key Methods:

```python
class DataValidationAgent:
    def run(df, file_type="FundHoldings") -> Dict
    def _validate_file_structure(df, template, file_type) -> Dict
    def _validate_mandatory_columns(df, template, file_type) -> Dict
    def _clean_data(df) -> Tuple[DataFrame, Dict]
    def _final_validation(df) -> Dict
    def validate_data_types(df) -> Dict
    def validate_with_llm_rules(df, file_type="FundHoldings") -> Dict  # NEW: Uses validation_rules.txt as LLM instructions
```

#### Validation Logic Implementation:

1. **Structure Validation**: Ensures uploaded file has expected number of columns
2. **Mandatory Columns**: Verifies all required columns are present
3. **Data Cleaning**: 
   - Removes rows with all financial fields zero/null
   - Removes rows where UNIQUE_ID starts with 'TOTAL'
   - Cleans UNIQUE_ID by removing special characters
4. **Final Validation**:
   - Checks file is not empty
   - Validates no null/empty UNIQUE_IDs
   - Ensures no duplicate UNIQUE_IDs
5. **Data Type Validation**: Validates column data types against template
6. **LLM-Enhanced Validation**: Uses `validation_rules.txt` as LLM instructions for comprehensive analysis

### 4. Test Suite (test_data_validation.py)

Comprehensive test coverage with 19 test cases:

- ✅ Template loading (success/failure)
- ✅ File structure validation (success/failure)
- ✅ Mandatory columns validation (success/failure)
- ✅ Data cleaning (zero removal, TOTAL removal, ID cleaning)
- ✅ Final validation (empty file, null IDs, duplicates, success)
- ✅ Data type validation (success/failure)
- ✅ LLM validation with validation_rules.txt (success/failure)
- ✅ Complete validation workflow (success/failure scenarios)

**All tests pass successfully!**

## Usage Examples

### Basic Usage:

```python
from core.data_validation import DataValidationAgent
import pandas as pd

# Initialize agent
agent = DataValidationAgent()

# Load your data
df = pd.read_excel("your_file.xlsx")

# Run complete validation
result = agent.run(df)

if result["success"]:
    print("✅ Validation passed!")
    cleaned_data = result["cleaned_data"]
else:
    print("❌ Validation failed:", result["message"])
```

### Individual Validations:

```python
# Run specific validations
structure_result = agent._validate_file_structure(df, template, "FundHoldings")
mandatory_result = agent._validate_mandatory_columns(df, template, "FundHoldings")
cleaned_df, clean_result = agent._clean_data(df)
final_result = agent._final_validation(cleaned_df)
dtype_result = agent.validate_data_types(df)

# Enhanced LLM validation using validation_rules.txt
llm_result = agent.validate_with_llm_rules(df)
```

## Architecture

### Separation of Concerns:

1. **file_validation.py**: 
   - File format validation (.xls/.xlsx)
   - Basic schema structure checking
   - LLM-based validation against templates

2. **data_validation.py**:
   - Business logic validation (stored procedure implementation)
   - Data cleaning and transformation
   - Comprehensive data quality checks
   - LLM-enhanced validation using validation_rules.txt as instructions

### Integration Points:

- Both agents use the same `schema_templates/column_template.json`
- Both can be used independently or in sequence
- File validation should run first, followed by data validation

## Expected Data Schema

Based on `column_template.json`:

```json
{
  "required_columns": [
    "UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV",
    "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES",
    "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"
  ],
  "data_types": {
    "UNIQUE_ID": "string",
    "PORTFOLIO_ID": "string", 
    "REGISTERED_HOLDER": "string",
    "NAV": "float",
    "OWNERSHIP_PERCENTAGE": "float",
    "CAPITAL_CALLED": "float",
    "NO_OF_SHARES": "int",
    "COMMITTED_CAPITAL": "float",
    "PERIOD": "string",
    "FUND_NAME": "string"
  }
}
```

## Running Tests

```bash
# Run all tests
python -m pytest test_data_validation.py -v

# Run specific test
python -m pytest test_data_validation.py::TestDataValidationAgent::test_validate_file_structure_success -v

# Run demo
python demo_data_validation.py
```

## Next Steps

1. **Integration**: Combine file_validation.py and data_validation.py in a unified workflow
2. **Error Handling**: Enhance error reporting and recovery mechanisms
3. **Performance**: Optimize for large datasets
4. **Configuration**: Make validation rules configurable
5. **Logging**: Implement structured logging for production use

## Dependencies

- pandas
- numpy (optional, removed from current implementation)
- langchain (for LLM integration)
- pytest (for testing)

The data validation agent is now fully functional and tested, implementing all the business logic from the stored procedures without requiring a database connection. **The system now uses `validation_rules.txt` as LLM instructions for enhanced validation analysis.**
