import json
import os
import pandas as pd
from core.llm_config import get_llm

class FileValidationAgent:
    def __init__(self):
        self.llm = get_llm()
        self.template_path = "schema_templates/column_template.json"

    def run(self, file_path: str, rules: list):
        logs = []
        result = {
            "success": True,
            "message": "",
            "logs": []
        }

        logs.append(f" File received: {os.path.basename(file_path)}")
        ext = os.path.splitext(file_path)[1].lower()
        logs.append(f" Detected file extension: {ext}")

       
        try:
            df = self._read_excel_with_engine(file_path, ext)
            preview = df.head(5).to_dict(orient="records")
            actual_columns = df.columns.tolist()
            logs.append(f"File loaded with {len(actual_columns)} columns, {len(df)} rows.")
        except Exception as e:
            logs.append(f" Could not read Excel file: {e}")
            result.update({
                "success": False,
                "message": str(e),
                "logs": logs
            })
            return result

        
        try:
            with open(self.template_path, "r") as f:
                template = json.load(f)
            logs.append(" Column template loaded successfully.")
        except Exception as e:
            logs.append(f" Failed to load template: {e}")
            template = {}

        # Step 3: Prepare prompt
        logs.append(" Validating with template and rules...")

        prompt = f"""
You are a file validation agent.

You will validate the uploaded Excel file based on:
1. Predefined validation rules (extracted from stored procedures)
2. A template defining expected columns and mandatory constraints

---

📘 Rules:
{json.dumps(rules, indent=2)}

📘 Template:
{json.dumps(template, indent=2)}

📄 Actual Columns: {actual_columns}

🧪 Sample Data:
{json.dumps(preview, indent=2)}

---

Return clear validation results. Mention any mismatches, missing columns, invalid values, and your final decision (valid or not).
"""

        # Step 4: Ask LLM
        response = self.llm.invoke(prompt).content

        result["message"] = response
        result["logs"] = logs
        return result

    def _read_excel_with_engine(self, file_path, ext):
        """Load Excel file using appropriate engine based on extension."""
        if ext == ".xls":
            return pd.read_excel(file_path, engine='xlrd')
        elif ext == ".xlsx":
            return pd.read_excel(file_path, engine='openpyxl')
        else:
            raise ValueError(f"Unsupported file extension: {ext}")
