import json
import os
import pandas as pd
from core.llm_config import get_llm

class FileValidationAgent:
    def __init__(self):
        self.llm = get_llm()
        self.template_path = "schema_templates/column_template.json"
        self.validation_rules_path = "store_procedure/validation_rules.txt"

    def run(self, file_path: str):
        logs = []
        result = {
            "success": True,
            "message": "",
            "logs": []
        }

        logs.append(f" File received: {os.path.basename(file_path)}")
        ext = os.path.splitext(file_path)[1].lower()
        logs.append(f" Detected file extension: {ext}")

       
        try:
            df = self._read_excel_with_engine(file_path, ext)
            preview = df.head(5).to_dict(orient="records")
            actual_columns = df.columns.tolist()
            logs.append(f"File loaded with {len(actual_columns)} columns, {len(df)} rows.")
        except Exception as e:
            logs.append(f" Could not read Excel file: {e}")
            result.update({
                "success": False,
                "message": str(e),
                "logs": logs
            })
            return result


        try:
            with open(self.template_path, "r") as f:
                template = json.load(f)
            logs.append(" Column template loaded successfully.")
        except Exception as e:
            logs.append(f" Failed to load template: {e}")
            template = {}

        # Load validation rules from validation_rules.txt
        try:
            with open(self.validation_rules_path, "r") as f:
                validation_rules = f.read()
            logs.append(" Validation rules loaded successfully.")
        except Exception as e:
            logs.append(f" Failed to load validation rules: {e}")
            validation_rules = "No validation rules available."

        # Step 3: Prepare prompt
        logs.append(" Validating with template and rules...")

        prompt = f"""
You are a file validation agent for CBRE FundHoldings data.

You will validate the uploaded Excel file based on:
1. File extension validation (must be .xls or .xlsx)
2. Schema validation against expected column structure
3. Validation rules from stored procedures

IMPORTANT: You should only validate file extension and schema structure. Do NOT perform data-level validations like checking for duplicates, null values, or data cleaning - those will be handled by a separate data validation agent.

---

📘 VALIDATION RULES (from stored procedures):
{validation_rules}

📘 EXPECTED SCHEMA TEMPLATE:
{json.dumps(template, indent=2)}

📄 ACTUAL FILE DETAILS:
- File Extension: {ext}
- Actual Columns: {actual_columns}
- Number of Columns: {len(actual_columns)}
- Number of Rows: {len(df)}

🧪 SAMPLE DATA (first 5 rows):
{json.dumps(preview, indent=2)}

---

VALIDATION TASKS:
1. Check if file extension is valid (.xls or .xlsx)
2. Verify that all required columns from the template are present
3. Check if column count matches expected count
4. Validate basic schema structure

Return a clear validation result with:
- Whether the file extension is valid
- Whether all required columns are present
- Whether the schema structure matches expectations
- Your final decision: VALID or INVALID for file/schema validation
- Any specific issues found

Do NOT validate data content, duplicates, null values, or perform data cleaning - focus only on file format and schema validation.
"""

        # Step 4: Ask LLM
        response = self.llm.invoke(prompt).content

        result["message"] = response
        result["logs"] = logs
        return result

    def _read_excel_with_engine(self, file_path, ext):
        """Load Excel file using appropriate engine based on extension."""
        if ext == ".xls":
            return pd.read_excel(file_path, engine='xlrd')
        elif ext == ".xlsx":
            return pd.read_excel(file_path, engine='openpyxl')
        else:
            raise ValueError(f"Unsupported file extension: {ext}")
