import json
import pandas as pd
import re
from typing import Dict, <PERSON><PERSON>, Any
from core.llm_config import get_llm


class DataValidationAgent:
    """
    Data validation agent that implements stored procedure logic for FundHoldings data validation.
    This agent performs data-level validations based on the business rules defined in stored procedures.
    """

    def __init__(self, llm=None):
        self.llm = llm if llm is not None else get_llm()
        self.template_path = "schema_templates/column_template.json"
        self.validation_results = []

    def run(self, df: pd.DataFrame, file_type: str = "FundHoldings") -> Dict[str, Any]:
        """
        Main validation method that runs all data validation procedures.

        Args:
            df: DataFrame containing the uploaded data
            file_type: Type of file being validated (default: "FundHoldings")

        Returns:
            Dict containing validation results, success status, and detailed logs
        """
        self.validation_results = []
        result = {
            "success": True,
            "message": "",
            "logs": [],
            "validation_details": {},
            "cleaned_data": None
        }

        try:
            # Load template for validation
            template = self._load_template()

            # Step 1: Validate file structure
            structure_result = self._validate_file_structure(df, template, file_type)
            result["validation_details"]["structure"] = structure_result
            result["logs"].extend(structure_result["logs"])

            if not structure_result["success"]:
                result["success"] = False
                result["message"] = structure_result["message"]
                return result

            # Step 2: Validate mandatory columns
            mandatory_result = self._validate_mandatory_columns(df, template, file_type)
            result["validation_details"]["mandatory_columns"] = mandatory_result
            result["logs"].extend(mandatory_result["logs"])

            if not mandatory_result["success"]:
                result["success"] = False
                result["message"] = mandatory_result["message"]
                return result

            # Step 3: Clean data
            cleaned_df, clean_result = self._clean_data(df.copy())
            result["validation_details"]["data_cleaning"] = clean_result
            result["logs"].extend(clean_result["logs"])
            result["cleaned_data"] = cleaned_df

            # Step 4: Final validation
            final_result = self._final_validation(cleaned_df)
            result["validation_details"]["final_validation"] = final_result
            result["logs"].extend(final_result["logs"])

            if not final_result["success"]:
                result["success"] = False
                result["message"] = final_result["message"]
                return result

            # All validations passed
            result["message"] = "All data validations passed successfully."

        except Exception as e:
            result["success"] = False
            result["message"] = f"Data validation error: {str(e)}"
            result["logs"].append(f"ERROR: {str(e)}")

        return result

    def _load_template(self) -> Dict[str, Any]:
        """Load the column template configuration."""
        try:
            with open(self.template_path, "r") as f:
                return json.load(f)
        except Exception as e:
            raise Exception(f"Failed to load template: {e}")

    def _validate_file_structure(self, df: pd.DataFrame, template: Dict, file_type: str) -> Dict[str, Any]:
        """
        Implements SP_VALIDATE_FILE_STRUCTURE logic.
        Validates that the uploaded file has the expected number of columns.
        """
        result = {
            "success": True,
            "message": "",
            "logs": []
        }

        expected_columns = template.get("required_columns", [])
        actual_columns = df.columns.tolist()

        col_count_template = len(expected_columns)
        col_count_upload = len(actual_columns)

        result["logs"].append(f"Expected columns count: {col_count_template}")
        result["logs"].append(f"Actual columns count: {col_count_upload}")

        if col_count_template != col_count_upload:
            result["success"] = False
            result["message"] = f"File structure mismatch. Expected {col_count_template} columns, found {col_count_upload}"
            result["logs"].append(f"VALIDATION FAILED: {result['message']}")
        else:
            result["message"] = "File structure valid"
            result["logs"].append("VALIDATION PASSED: File structure is valid")

        return result

    def _validate_mandatory_columns(self, df: pd.DataFrame, template: Dict, file_type: str) -> Dict[str, Any]:
        """
        Implements SP_VALIDATE_MANDATORY_COLUMNS logic.
        Validates that all required columns are present in the uploaded file.
        """
        result = {
            "success": True,
            "message": "",
            "logs": []
        }

        required_columns = template.get("required_columns", [])
        actual_columns = df.columns.tolist()

        # Convert to uppercase for case-insensitive comparison
        actual_columns_upper = [col.upper() for col in actual_columns]
        required_columns_upper = [col.upper() for col in required_columns]

        missing_columns = []
        for req_col in required_columns_upper:
            if req_col not in actual_columns_upper:
                missing_columns.append(req_col)

        missing_count = len(missing_columns)
        result["logs"].append(f"Required columns: {required_columns}")
        result["logs"].append(f"Missing columns count: {missing_count}")

        if missing_count > 0:
            result["success"] = False
            result["message"] = f"Missing required columns: {', '.join(missing_columns)}"
            result["logs"].append(f"VALIDATION FAILED: {result['message']}")
        else:
            result["message"] = "All required columns present"
            result["logs"].append("VALIDATION PASSED: All required columns are present")

        return result

    def _clean_data(self, df: pd.DataFrame) -> Tuple[pd.DataFrame, Dict[str, Any]]:
        """
        Implements SP_CLEAN_DATA logic.
        Cleans the data by removing invalid rows and cleaning unique IDs.
        """
        result = {
            "success": True,
            "message": "",
            "logs": []
        }

        original_count = len(df)
        result["logs"].append(f"Original data count: {original_count}")

        # Step 1: Delete rows with all financial fields null/zero
        financial_columns = ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']

        # Convert financial columns to numeric, replacing non-numeric with 0
        for col in financial_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce').fillna(0)

        # Create condition for rows where all financial fields are 0 or null
        financial_zero_condition = True
        for col in financial_columns:
            if col in df.columns:
                financial_zero_condition = financial_zero_condition & (df[col] == 0)

        rows_before_financial_cleanup = len(df)
        df = df[~financial_zero_condition.values]
        rows_after_financial_cleanup = len(df)
        removed_financial = rows_before_financial_cleanup - rows_after_financial_cleanup

        result["logs"].append(f"Removed {removed_financial} rows with all financial fields zero/null")

        # Step 2: Delete rows where unique ID starts with 'TOTAL'
        if 'UNIQUE_ID' in df.columns:
            rows_before_total_cleanup = len(df)
            df = df[~df['UNIQUE_ID'].astype(str).str.upper().str.startswith('TOTAL')]
            rows_after_total_cleanup = len(df)
            removed_total = rows_before_total_cleanup - rows_after_total_cleanup

            result["logs"].append(f"Removed {removed_total} rows with UNIQUE_ID starting with 'TOTAL'")

            # Step 3: Clean unique ID - remove special characters and parentheses
            df['UNIQUE_ID'] = df['UNIQUE_ID'].astype(str).apply(
                lambda x: re.sub(r'[^A-Za-z0-9]', '', str(x)) if pd.notna(x) else x
            )
            result["logs"].append("Cleaned UNIQUE_ID by removing special characters")

        final_count = len(df)
        total_removed = original_count - final_count

        result["message"] = f"Data cleaned successfully. Removed {total_removed} rows."
        result["logs"].append(f"Final data count: {final_count}")

        return df, result

    def _final_validation(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Implements SP_FINAL_VALIDATION logic.
        Performs final validation checks on the cleaned data.
        """
        result = {
            "success": True,
            "message": "",
            "logs": []
        }

        # Check 1: File is not empty
        rec_count = len(df)
        result["logs"].append(f"Record count: {rec_count}")

        if rec_count == 0:
            result["success"] = False
            result["message"] = "Validation failed: File is empty."
            result["logs"].append(f"VALIDATION FAILED: {result['message']}")
            return result

        # Check 2: No null unique IDs
        if 'UNIQUE_ID' in df.columns:
            null_uid_condition = df['UNIQUE_ID'].isna() | (df['UNIQUE_ID'].astype(str).str.strip() == '')
            null_uid_count = null_uid_condition.sum()

            result["logs"].append(f"Null/empty UNIQUE_ID count: {null_uid_count}")

            if null_uid_count > 0:
                result["success"] = False
                result["message"] = "Validation failed: Unique Identifier missing."
                result["logs"].append(f"VALIDATION FAILED: {result['message']}")
                return result

            # Check 3: No duplicate unique IDs
            duplicate_uids = df['UNIQUE_ID'].duplicated()
            dup_count = duplicate_uids.sum()

            result["logs"].append(f"Duplicate UNIQUE_ID count: {dup_count}")

            if dup_count > 0:
                result["success"] = False
                result["message"] = "Validation failed: Duplicate Unique Identifiers."
                result["logs"].append(f"VALIDATION FAILED: {result['message']}")
                # Log the duplicate IDs for debugging
                duplicate_ids = df[duplicate_uids]['UNIQUE_ID'].tolist()
                result["logs"].append(f"Duplicate IDs found: {duplicate_ids}")
                return result

        result["message"] = "Final validation passed."
        result["logs"].append("VALIDATION PASSED: All final validation checks passed")

        return result

    def validate_data_types(self, df: pd.DataFrame) -> Dict[str, Any]:
        """
        Additional method to validate data types based on template.
        This is not part of the stored procedures but adds extra validation.
        """
        result = {
            "success": True,
            "message": "",
            "logs": [],
            "type_errors": []
        }

        try:
            template = self._load_template()
            data_types = template.get("data_types", {})

            for column, expected_type in data_types.items():
                if column in df.columns:
                    if expected_type == "float":
                        try:
                            pd.to_numeric(df[column], errors='raise')
                        except (ValueError, TypeError):
                            result["type_errors"].append(f"{column}: Expected numeric, found non-numeric values")

                    elif expected_type == "int":
                        try:
                            pd.to_numeric(df[column], errors='raise', downcast='integer')
                        except (ValueError, TypeError):
                            result["type_errors"].append(f"{column}: Expected integer, found non-integer values")

                    elif expected_type == "string":
                        # String validation - check for reasonable string values
                        if df[column].dtype not in ['object', 'string']:
                            result["type_errors"].append(f"{column}: Expected string, found {df[column].dtype}")

            if result["type_errors"]:
                result["success"] = False
                result["message"] = f"Data type validation failed: {len(result['type_errors'])} errors found"
                result["logs"].extend(result["type_errors"])
            else:
                result["message"] = "All data types are valid"
                result["logs"].append("VALIDATION PASSED: Data types validation successful")

        except Exception as e:
            result["success"] = False
            result["message"] = f"Data type validation error: {str(e)}"
            result["logs"].append(f"ERROR: {str(e)}")

        return result