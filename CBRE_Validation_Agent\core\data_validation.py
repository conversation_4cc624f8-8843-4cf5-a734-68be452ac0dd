import pandas as pd
from typing import Dict, Any
from core.llm_config import get_llm


class DataValidationAgent:
    """
    Data validation agent that validates uploaded file data row by row according to validation_rules_updated.json.
    Returns original data with Is_correct and Why columns.
    """

    def __init__(self, llm=None):
        self.llm = llm or get_llm()
        self.validation_rules_path = "store_procedure/validation_rules_updated.json"

    def validate_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        Validate uploaded file data row by row according to validation_rules_updated.json.

        Args:
            df: DataFrame containing the uploaded data

        Returns:
            DataFrame with original data plus Is_correct and Why columns
        """
        result_df = df.copy()

        result_df['Is_correct'] = True
        result_df['Why'] = ''

        validation_rules = self._load_validation_rules()

        for index, row in result_df.iterrows():
            is_correct, error_message = self._validate_row(row, validation_rules)
            result_df.at[index, 'Is_correct'] = is_correct
            result_df.at[index, 'Why'] = error_message

        return result_df

    def _load_validation_rules(self) -> str:
        """Load the validation rules from validation_rules_updated.json."""
        try:
            with open(self.validation_rules_path, "r") as f:
                return f.read()
        except Exception as e:
            raise Exception(f"Failed to load validation rules: {e}")

    def _validate_row(self, row: pd.Series, validation_rules: str) -> tuple:
        """
        Validate a single row according to validation rules.

        Args:
            row: Single row from DataFrame
            validation_rules: Validation rules from validation_rules_updated.json

        Returns:
            tuple: (is_correct: bool, error_message: str)
        """
        errors = []

        # Check 1: UNIQUE_ID validation
        unique_id = row.get('UNIQUE_ID', '')
        if pd.isna(unique_id) or str(unique_id).strip() == '':
            errors.append("UNIQUE_ID is missing or empty")
        elif str(unique_id).upper().startswith('TOTAL'):
            errors.append("UNIQUE_ID starts with 'TOTAL' (should be excluded)")

        # Check 2: Financial fields validation (from SP_CLEAN_DATA logic)
        financial_fields = ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']
        all_financial_zero = True

        for field in financial_fields:
            if field in row:
                value = row[field]
                if pd.notna(value) and value != 0:
                    all_financial_zero = False
                    break

        if all_financial_zero:
            errors.append("All financial fields (NAV, OWNERSHIP_PERCENTAGE, CAPITAL_CALLED, NO_OF_SHARES, COMMITTED_CAPITAL) are zero or null")

        # Check 3: Required fields validation
        required_fields = ['UNIQUE_ID', 'PORTFOLIO_ID', 'REGISTERED_HOLDER', 'FUND_NAME']
        for field in required_fields:
            if field not in row or pd.isna(row[field]) or str(row[field]).strip() == '':
                errors.append(f"Required field '{field}' is missing or empty")

        # Check 4: Data type validation
        numeric_fields = ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']
        for field in numeric_fields:
            if field in row and pd.notna(row[field]):
                try:
                    float(row[field])
                except (ValueError, TypeError):
                    errors.append(f"Field '{field}' should be numeric but contains invalid value: {row[field]}")

        # Check 5: NO_OF_SHARES should be integer
        if 'NO_OF_SHARES' in row and pd.notna(row['NO_OF_SHARES']):
            try:
                val = float(row['NO_OF_SHARES'])
                if val != int(val):
                    errors.append("NO_OF_SHARES should be an integer")
            except (ValueError, TypeError):
                pass  

        if errors:
            return False, "; ".join(errors)
        else:
            return True, ""