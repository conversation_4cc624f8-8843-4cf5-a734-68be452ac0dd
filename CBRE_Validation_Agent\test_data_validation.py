import unittest
import pandas as pd
import numpy as np
import os
import sys
import json
from unittest.mock import patch, mock_open

# Add the core module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

from core.data_validation import DataValidationAgent


class TestDataValidationAgent(unittest.TestCase):
    """Test suite for DataValidationAgent class."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create a simple mock LLM object to avoid OpenAI dependency in tests
        class MockLLM:
            def invoke(self, prompt):
                class MockResponse:
                    content = "Mock LLM response"
                return MockResponse()

        self.agent = DataValidationAgent(llm=MockLLM())
        
        # Mock template data
        self.mock_template = {
            "required_columns": [
                "UNIQUE_ID", "PORTFOLIO_ID", "REGISTERED_HOLDER", "NAV",
                "OWNERSHIP_PERCENTAGE", "CAPITAL_CALLED", "NO_OF_SHARES",
                "COMMITTED_CAPITAL", "PERIOD", "FUND_NAME"
            ],
            "data_types": {
                "UNIQUE_ID": "string",
                "PORTFOLIO_ID": "string", 
                "REGISTERED_HOLDER": "string",
                "NAV": "float",
                "OWNERSHIP_PERCENTAGE": "float",
                "CAPITAL_CALLED": "float",
                "NO_OF_SHARES": "int",
                "COMMITTED_CAPITAL": "float",
                "PERIOD": "string",
                "FUND_NAME": "string"
            }
        }
        
        # Create valid test DataFrame
        self.valid_df = pd.DataFrame({
            'UNIQUE_ID': ['ID001', 'ID002', 'ID003'],
            'PORTFOLIO_ID': ['P001', 'P002', 'P003'],
            'REGISTERED_HOLDER': ['Holder1', 'Holder2', 'Holder3'],
            'NAV': [100.0, 200.0, 300.0],
            'OWNERSHIP_PERCENTAGE': [10.5, 20.5, 30.5],
            'CAPITAL_CALLED': [1000.0, 2000.0, 3000.0],
            'NO_OF_SHARES': [100, 200, 300],
            'COMMITTED_CAPITAL': [5000.0, 6000.0, 7000.0],
            'PERIOD': ['2023-Q1', '2023-Q2', '2023-Q3'],
            'FUND_NAME': ['Fund A', 'Fund B', 'Fund C']
        })

    @patch('builtins.open', new_callable=mock_open)
    @patch('json.load')
    def test_load_template_success(self, mock_json_load, mock_file):
        """Test successful template loading."""
        mock_json_load.return_value = self.mock_template
        
        template = self.agent._load_template()
        
        self.assertEqual(template, self.mock_template)
        mock_file.assert_called_once_with(self.agent.template_path, "r")

    @patch('builtins.open', side_effect=FileNotFoundError("Template not found"))
    def test_load_template_failure(self, mock_file):
        """Test template loading failure."""
        with self.assertRaises(Exception) as context:
            self.agent._load_template()
        
        self.assertIn("Failed to load template", str(context.exception))

    def test_validate_file_structure_success(self):
        """Test successful file structure validation."""
        result = self.agent._validate_file_structure(self.valid_df, self.mock_template, "FundHoldings")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "File structure valid")
        self.assertIn("Expected columns count: 10", result["logs"][0])
        self.assertIn("Actual columns count: 10", result["logs"][1])

    def test_validate_file_structure_failure(self):
        """Test file structure validation failure with wrong column count."""
        # Create DataFrame with fewer columns
        invalid_df = self.valid_df.drop(['FUND_NAME', 'PERIOD'], axis=1)
        
        result = self.agent._validate_file_structure(invalid_df, self.mock_template, "FundHoldings")
        
        self.assertFalse(result["success"])
        self.assertIn("File structure mismatch", result["message"])
        self.assertIn("Expected 10 columns, found 8", result["message"])

    def test_validate_mandatory_columns_success(self):
        """Test successful mandatory columns validation."""
        result = self.agent._validate_mandatory_columns(self.valid_df, self.mock_template, "FundHoldings")
        
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "All required columns present")

    def test_validate_mandatory_columns_failure(self):
        """Test mandatory columns validation failure with missing columns."""
        # Create DataFrame missing required columns
        invalid_df = self.valid_df.drop(['UNIQUE_ID', 'NAV'], axis=1)
        
        result = self.agent._validate_mandatory_columns(invalid_df, self.mock_template, "FundHoldings")
        
        self.assertFalse(result["success"])
        self.assertIn("Missing required columns", result["message"])
        self.assertIn("UNIQUE_ID", result["message"])
        self.assertIn("NAV", result["message"])

    def test_clean_data_remove_zero_financial_rows(self):
        """Test data cleaning removes rows with all zero financial values."""
        # Create DataFrame with rows having all zero financial values
        test_df = self.valid_df.copy()
        test_df.loc[1, ['NAV', 'OWNERSHIP_PERCENTAGE', 'CAPITAL_CALLED', 'NO_OF_SHARES', 'COMMITTED_CAPITAL']] = 0
        
        cleaned_df, result = self.agent._clean_data(test_df)
        
        self.assertTrue(result["success"])
        self.assertEqual(len(cleaned_df), 2)  # Should remove 1 row
        self.assertIn("Removed 1 rows with all financial fields zero/null", result["logs"][1])

    def test_clean_data_remove_total_rows(self):
        """Test data cleaning removes rows with UNIQUE_ID starting with 'TOTAL'."""
        test_df = self.valid_df.copy()
        test_df.loc[1, 'UNIQUE_ID'] = 'TOTAL_SUMMARY'
        
        cleaned_df, result = self.agent._clean_data(test_df)
        
        self.assertTrue(result["success"])
        self.assertEqual(len(cleaned_df), 2)  # Should remove 1 row
        self.assertIn("Removed 1 rows with UNIQUE_ID starting with 'TOTAL'", result["logs"][2])

    def test_clean_data_clean_unique_id(self):
        """Test data cleaning removes special characters from UNIQUE_ID."""
        test_df = self.valid_df.copy()
        test_df.loc[0, 'UNIQUE_ID'] = 'ID-001@#$'
        
        cleaned_df, result = self.agent._clean_data(test_df)
        
        self.assertTrue(result["success"])
        self.assertEqual(cleaned_df.loc[0, 'UNIQUE_ID'], 'ID001')
        self.assertIn("Cleaned UNIQUE_ID by removing special characters", result["logs"][3])

    def test_final_validation_success(self):
        """Test successful final validation."""
        result = self.agent._final_validation(self.valid_df)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "Final validation passed.")

    def test_final_validation_empty_file(self):
        """Test final validation failure with empty DataFrame."""
        empty_df = pd.DataFrame()
        
        result = self.agent._final_validation(empty_df)
        
        self.assertFalse(result["success"])
        self.assertEqual(result["message"], "Validation failed: File is empty.")

    def test_final_validation_null_unique_id(self):
        """Test final validation failure with null UNIQUE_ID."""
        test_df = self.valid_df.copy()
        test_df.loc[1, 'UNIQUE_ID'] = None
        
        result = self.agent._final_validation(test_df)
        
        self.assertFalse(result["success"])
        self.assertEqual(result["message"], "Validation failed: Unique Identifier missing.")

    def test_final_validation_duplicate_unique_id(self):
        """Test final validation failure with duplicate UNIQUE_ID."""
        test_df = self.valid_df.copy()
        test_df.loc[1, 'UNIQUE_ID'] = 'ID001'  # Same as row 0
        
        result = self.agent._final_validation(test_df)
        
        self.assertFalse(result["success"])
        self.assertEqual(result["message"], "Validation failed: Duplicate Unique Identifiers.")

    @patch.object(DataValidationAgent, '_load_template')
    def test_validate_data_types_success(self, mock_load_template):
        """Test successful data type validation."""
        mock_load_template.return_value = self.mock_template
        
        result = self.agent.validate_data_types(self.valid_df)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "All data types are valid")

    @patch.object(DataValidationAgent, '_load_template')
    def test_validate_data_types_failure(self, mock_load_template):
        """Test data type validation failure."""
        mock_load_template.return_value = self.mock_template
        
        # Create DataFrame with invalid data types
        invalid_df = self.valid_df.copy()
        invalid_df['NAV'] = ['invalid', 'data', 'types']
        
        result = self.agent.validate_data_types(invalid_df)
        
        self.assertFalse(result["success"])
        self.assertIn("Data type validation failed", result["message"])

    @patch.object(DataValidationAgent, '_load_template')
    @patch.object(DataValidationAgent, '_validate_file_structure')
    @patch.object(DataValidationAgent, '_validate_mandatory_columns')
    @patch.object(DataValidationAgent, '_clean_data')
    @patch.object(DataValidationAgent, '_final_validation')
    def test_run_success(self, mock_final, mock_clean, mock_mandatory, mock_structure, mock_template):
        """Test successful complete validation run."""
        # Mock all validation steps to return success
        mock_template.return_value = self.mock_template
        mock_structure.return_value = {"success": True, "message": "Structure valid", "logs": ["Structure OK"]}
        mock_mandatory.return_value = {"success": True, "message": "Columns valid", "logs": ["Columns OK"]}
        mock_clean.return_value = (self.valid_df, {"success": True, "message": "Data cleaned", "logs": ["Clean OK"]})
        mock_final.return_value = {"success": True, "message": "Final valid", "logs": ["Final OK"]}
        
        result = self.agent.run(self.valid_df)
        
        self.assertTrue(result["success"])
        self.assertEqual(result["message"], "All data validations passed successfully.")
        self.assertIsNotNone(result["cleaned_data"])

    @patch.object(DataValidationAgent, '_load_template')
    @patch.object(DataValidationAgent, '_validate_file_structure')
    def test_run_structure_failure(self, mock_structure, mock_template):
        """Test validation run failure at structure validation step."""
        mock_template.return_value = self.mock_template
        mock_structure.return_value = {"success": False, "message": "Structure invalid", "logs": ["Structure failed"]}
        
        result = self.agent.run(self.valid_df)
        
        self.assertFalse(result["success"])
        self.assertEqual(result["message"], "Structure invalid")


if __name__ == '__main__':
    unittest.main()
