#!/usr/bin/env python3
"""
Test script for DataValidationAgent
Usage: python test_data_validation.py <file_path>
"""

import pandas as pd
import sys
import os

# Add the core module to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'core'))

from core.data_validation import DataValidationAgent


def test_file_validation(file_path: str):
    """
    Test data validation on an uploaded file.
    
    Args:
        file_path: Path to the Excel file to validate
    """
    print("=" * 60)
    print("CBRE Data Validation Test")
    print("=" * 60)
    
    # Initialize the validation agent with actual OpenAI LLM from .env
    agent = DataValidationAgent()
    
    try:
        # Load the file
        print(f"\n📁 Loading file: {file_path}")
        
        if not os.path.exists(file_path):
            print(f"❌ ERROR: File not found: {file_path}")
            return
        
        # Read Excel file
        if file_path.endswith('.xlsx'):
            df = pd.read_excel(file_path, engine='openpyxl')
        elif file_path.endswith('.xls'):
            df = pd.read_excel(file_path, engine='xlrd')
        else:
            print(f"❌ ERROR: Unsupported file format. Only .xls and .xlsx files are supported.")
            return
        
        print(f"✅ File loaded successfully!")
        print(f"   - Rows: {len(df)}")
        print(f"   - Columns: {len(df.columns)}")
        print(f"   - Column names: {list(df.columns)}")
        
        # Show sample data
        print(f"\n📊 Sample data (first 3 rows):")
        print(df.head(3).to_string())
        
        # Run validation
        print(f"\n🔍 Running row-by-row validation...")
        result_df = agent.validate_data(df)
        
        # Show results
        print(f"\n📋 Validation Results:")
        print("=" * 40)
        
        # Count results
        total_rows = len(result_df)
        correct_rows = result_df['Is_correct'].sum()
        incorrect_rows = total_rows - correct_rows
        
        print(f"Total rows processed: {total_rows}")
        print(f"✅ Correct rows: {correct_rows}")
        print(f"❌ Incorrect rows: {incorrect_rows}")
        
        if incorrect_rows > 0:
            print(f"\n❌ Rows with errors:")
            error_rows = result_df[result_df['Is_correct'] == False]
            for idx, row in error_rows.iterrows():
                print(f"   Row {idx + 1}: {row['Why']}")
        
        # Show full results
        print(f"\n📄 Complete validation results:")
        print(result_df.to_string())
        
        # Save results to file
        output_file = file_path.replace('.xlsx', '_validated.xlsx').replace('.xls', '_validated.xlsx')
        result_df.to_excel(output_file, index=False)
        print(f"\n💾 Results saved to: {output_file}")
        
    except Exception as e:
        print(f"❌ ERROR: {str(e)}")
        import traceback
        traceback.print_exc()


def create_sample_test_file():
    """Create a sample test file for demonstration."""
    sample_data = {
        'UNIQUE_ID': ['FUND001', 'FUND002', 'TOTAL_SUMMARY', '', 'FUND005'],
        'PORTFOLIO_ID': ['PORT001', 'PORT002', 'PORT003', 'PORT004', 'PORT005'],
        'REGISTERED_HOLDER': ['Holder A', 'Holder B', 'Holder C', 'Holder D', ''],
        'NAV': [1000.50, 0, 0, 0, 2500.75],
        'OWNERSHIP_PERCENTAGE': [15.5, 0, 0, 0, 22.3],
        'CAPITAL_CALLED': [50000.00, 0, 0, 0, 75000.00],
        'NO_OF_SHARES': [100, 0, 0, 0, 250],
        'COMMITTED_CAPITAL': [100000.00, 0, 0, 0, 150000.00],
        'PERIOD': ['2023-Q1', '2023-Q2', '2023-Q3', '2023-Q4', '2024-Q1'],
        'FUND_NAME': ['Growth Fund A', 'Value Fund B', 'Tech Fund C', '', 'Bond Fund E']
    }
    
    df = pd.DataFrame(sample_data)
    sample_file = "sample_test_data.xlsx"
    df.to_excel(sample_file, index=False)
    print(f"📁 Sample test file created: {sample_file}")
    return sample_file


if __name__ == "__main__":
    # 🎯 ADD YOUR FILE PATH HERE:
    file_path = "YOUR_FILE_PATH_HERE.xlsx"  # ← CHANGE THIS TO YOUR ACTUAL FILE PATH

    # Example paths:
    # file_path = r"C:\Users\<USER>\OneDrive\Desktop\my_data.xlsx"
    # file_path = r"C:\Users\<USER>\Documents\fund_data.xlsx"
    # file_path = "data/FundHoldings_WithErrors.xlsx"  # relative path

    print("=" * 60)
    print("CBRE Data Validation Test")
    print("=" * 60)

    if file_path == "YOUR_FILE_PATH_HERE.xlsx":
        print("\n❌ ERROR: Please update the file_path variable in the code!")
        print("\n📝 INSTRUCTIONS:")
        print("1. Open test_data_validation.py")
        print("2. Find line: file_path = \"YOUR_FILE_PATH_HERE.xlsx\"")
        print("3. Replace with your actual file path")
        print("4. Save and run again")
        print("\n🔧 Or use sample data:")
        sample_file = create_sample_test_file()
        print(f"   Sample file created: {sample_file}")
        print(f"   Update file_path = \"{sample_file}\" and run again")
        print("\n" + "=" * 60)
    else:
        print(f"\n🎯 Processing file: {file_path}")
        test_file_validation(file_path)
