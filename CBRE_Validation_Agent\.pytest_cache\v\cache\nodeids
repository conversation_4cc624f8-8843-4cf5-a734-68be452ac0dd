["test_data_validation.py::TestDataValidationAgent::test_clean_data_clean_unique_id", "test_data_validation.py::TestDataValidationAgent::test_clean_data_remove_total_rows", "test_data_validation.py::TestDataValidationAgent::test_clean_data_remove_zero_financial_rows", "test_data_validation.py::TestDataValidationAgent::test_final_validation_duplicate_unique_id", "test_data_validation.py::TestDataValidationAgent::test_final_validation_empty_file", "test_data_validation.py::TestDataValidationAgent::test_final_validation_null_unique_id", "test_data_validation.py::TestDataValidationAgent::test_final_validation_success", "test_data_validation.py::TestDataValidationAgent::test_load_template_failure", "test_data_validation.py::TestDataValidationAgent::test_load_template_success", "test_data_validation.py::TestDataValidationAgent::test_run_structure_failure", "test_data_validation.py::TestDataValidationAgent::test_run_success", "test_data_validation.py::TestDataValidationAgent::test_validate_data_types_failure", "test_data_validation.py::TestDataValidationAgent::test_validate_data_types_success", "test_data_validation.py::TestDataValidationAgent::test_validate_file_structure_failure", "test_data_validation.py::TestDataValidationAgent::test_validate_file_structure_success", "test_data_validation.py::TestDataValidationAgent::test_validate_mandatory_columns_failure", "test_data_validation.py::TestDataValidationAgent::test_validate_mandatory_columns_success", "test_data_validation.py::TestDataValidationAgent::test_validate_with_llm_rules_failure", "test_data_validation.py::TestDataValidationAgent::test_validate_with_llm_rules_success"]