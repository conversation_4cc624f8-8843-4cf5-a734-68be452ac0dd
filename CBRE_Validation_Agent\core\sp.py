from core.llm_config import get_llm
from core.file_validation import FileValidationAgent
from langchain.schema import SystemMessage, HumanMessage
import json
import re

class MasterAgent:
    def __init__(self):
        self.llm = get_llm()
        self.file_validator = FileValidationAgent()
        self.sp_path = "store_procedure/validation_rules.txt"

    def extract_validation_rules(self):
        with open(self.sp_path, "r", encoding="utf-8") as f:
            sp_code = f.read()

        messages = [
            SystemMessage(content="You are an expert validator agent that reads SQL stored procedure logic and outputs validation rules in raw JSON list format ONLY. Do not explain, do not wrap in markdown."),
            HumanMessage(content=f"""
Extract validation rules from this stored procedure. Only return a JSON list of rules. Example format:
[
  {{
    "field": "extension",
    "rule": "must be .xls or .xlsx",
    "level": "error"
  }}
]

Stored Procedure:
{sp_code}
""")
        ]

        response = self.llm.invoke(messages).content
        print("\n LLM Raw Response Type:", type(response))
        print(" LLM Raw Response Content:", response)

        if isinstance(response, str):
            response = re.sub(r"```json|```", "", response).strip()
        try:
            if isinstance(response, str):
                rules = json.loads(response)
            elif isinstance(response, list):
                rules = response
            else:
                print(" Unexpected LLM response type:", type(response))
                rules = []
        except json.JSONDecodeError:
            print(" JSONDecodeError: Could not parse LLM response.")
            rules = []

        return rules 

    def run(self, file_path: str):

        rules = self.extract_validation_rules()
        print(" Extracted Rules from SP:", rules)
        file_validation_result = self.file_validator.run(file_path, rules)

        print("\n File Validation Logs:")
        for log in file_validation_result["logs"]:
            print(" -", log)

        print("\n LLM Response:", file_validation_result["message"])

        return file_validation_result
